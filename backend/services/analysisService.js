const sharp = require('sharp');
const fs = require('fs').promises;

class AnalysisService {
    /**
     * Detects focal zones in an image using edge detection and contrast analysis
     * @param {string} imagePath - Path to the image file
     * @returns {Object} Analysis result with focal zones
     */
    async detectFocalZones(imagePath) {
        try {
            const image = sharp(imagePath);
            const metadata = await image.metadata();
            
            // Get image dimensions
            const { width, height } = metadata;
            
            // Convert to grayscale for analysis
            const grayscaleBuffer = await image
                .grayscale()
                .raw()
                .toBuffer();
            
            // Analyze image for focus areas using edge detection
            const focusMap = await this.calculateFocusMap(grayscaleBuffer, width, height);
            
            // Find the most focused regions
            const focalZones = this.findFocalZones(focusMap, width, height);
            
            return {
                width,
                height,
                focalZones,
                suggestedFocusZone: focalZones[0] || { x: 0.5, y: 0.5, radius: 0.3 },
                analysisComplete: true
            };
            
        } catch (error) {
            console.error('Error in detectFocalZones:', error);
            throw new Error(`Failed to analyze image: ${error.message}`);
        }
    }
    
    /**
     * Detects focal zones from base64 image data
     * @param {string} base64Data - Base64 encoded image data
     * @returns {Object} Analysis result with focal zones
     */
    async detectFocalZonesFromBase64(base64Data) {
        try {
            // Remove data URL prefix if present
            const base64Image = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
            const imageBuffer = Buffer.from(base64Image, 'base64');
            
            const image = sharp(imageBuffer);
            const metadata = await image.metadata();
            
            // Get image dimensions
            const { width, height } = metadata;
            
            // Convert to grayscale for analysis
            const grayscaleBuffer = await image
                .grayscale()
                .raw()
                .toBuffer();
            
            // Analyze image for focus areas
            const focusMap = await this.calculateFocusMap(grayscaleBuffer, width, height);
            
            // Find the most focused regions
            const focalZones = this.findFocalZones(focusMap, width, height);
            
            return {
                width,
                height,
                focalZones,
                suggestedFocusZone: focalZones[0] || { x: 0.5, y: 0.5, radius: 0.3 },
                analysisComplete: true
            };
            
        } catch (error) {
            console.error('Error in detectFocalZonesFromBase64:', error);
            throw new Error(`Failed to analyze base64 image: ${error.message}`);
        }
    }
    
    /**
     * Calculates a focus map using Laplacian edge detection
     * @param {Buffer} grayscaleBuffer - Grayscale image buffer
     * @param {number} width - Image width
     * @param {number} height - Image height
     * @returns {Array} 2D array representing focus strength at each pixel
     */
    async calculateFocusMap(grayscaleBuffer, width, height) {
        const focusMap = [];
        const windowSize = 5; // Size of the analysis window
        const halfWindow = Math.floor(windowSize / 2);
        
        // Initialize focus map
        for (let y = 0; y < height; y++) {
            focusMap[y] = new Array(width).fill(0);
        }
        
        // Apply Laplacian filter for edge detection
        for (let y = halfWindow; y < height - halfWindow; y++) {
            for (let x = halfWindow; x < width - halfWindow; x++) {
                let laplacian = 0;
                
                // Apply 3x3 Laplacian kernel
                const center = grayscaleBuffer[y * width + x];
                const top = grayscaleBuffer[(y - 1) * width + x];
                const bottom = grayscaleBuffer[(y + 1) * width + x];
                const left = grayscaleBuffer[y * width + (x - 1)];
                const right = grayscaleBuffer[y * width + (x + 1)];
                
                laplacian = Math.abs(-4 * center + top + bottom + left + right);
                focusMap[y][x] = laplacian;
            }
        }
        
        return focusMap;
    }
    
    /**
     * Finds focal zones from the focus map
     * @param {Array} focusMap - 2D array of focus values
     * @param {number} width - Image width
     * @param {number} height - Image height
     * @returns {Array} Array of focal zone objects
     */
    findFocalZones(focusMap, width, height) {
        const zones = [];
        const gridSize = 20; // Divide image into grid for analysis
        const cellWidth = width / gridSize;
        const cellHeight = height / gridSize;
        
        // Calculate average focus for each grid cell
        for (let gridY = 0; gridY < gridSize; gridY++) {
            for (let gridX = 0; gridX < gridSize; gridX++) {
                const startX = Math.floor(gridX * cellWidth);
                const endX = Math.floor((gridX + 1) * cellWidth);
                const startY = Math.floor(gridY * cellHeight);
                const endY = Math.floor((gridY + 1) * cellHeight);
                
                let totalFocus = 0;
                let pixelCount = 0;
                
                for (let y = startY; y < endY && y < height; y++) {
                    for (let x = startX; x < endX && x < width; x++) {
                        totalFocus += focusMap[y][x];
                        pixelCount++;
                    }
                }
                
                const averageFocus = pixelCount > 0 ? totalFocus / pixelCount : 0;
                
                if (averageFocus > 0) {
                    zones.push({
                        x: (startX + endX) / (2 * width), // Normalized coordinates
                        y: (startY + endY) / (2 * height),
                        radius: Math.min(cellWidth, cellHeight) / (2 * Math.max(width, height)),
                        strength: averageFocus
                    });
                }
            }
        }
        
        // Sort zones by focus strength and return top candidates
        zones.sort((a, b) => b.strength - a.strength);
        return zones.slice(0, 5); // Return top 5 focal zones
    }
}

module.exports = new AnalysisService();
