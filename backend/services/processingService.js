const sharp = require('sharp');

class ProcessingService {
    /**
     * Applies focus/blur effects to an image
     * @param {string} imagePath - Path to the image file
     * @param {Object} options - Processing options
     * @returns {Buffer} Processed image buffer
     */
    async applyFocusEffect(imagePath, options) {
        try {
            const { blurIntensity, focusZone, focusIntensity } = options;
            
            const image = sharp(imagePath);
            const metadata = await image.metadata();
            const { width, height } = metadata;
            
            // Create a mask for the focus area
            const focusMask = await this.createFocusMask(width, height, focusZone);
            
            // Apply blur to the entire image
            const blurredImage = await image
                .blur(blurIntensity)
                .toBuffer();
            
            // Get the original image
            const originalImage = await sharp(imagePath).toBuffer();
            
            // Composite the original and blurred images using the mask
            const result = await sharp(blurredImage)
                .composite([
                    {
                        input: originalImage,
                        blend: 'over',
                        opacity: focusIntensity
                    }
                ])
                .jpeg({ quality: 90 })
                .toBuffer();
            
            return result;
            
        } catch (error) {
            console.error('Error in applyFocusEffect:', error);
            throw new Error(`Failed to process image: ${error.message}`);
        }
    }
    
    /**
     * Applies focus/blur effects to a base64 encoded image
     * @param {string} base64Data - Base64 encoded image data
     * @param {Object} options - Processing options
     * @returns {Buffer} Processed image buffer
     */
    async applyFocusEffectFromBase64(base64Data, options) {
        try {
            // Remove data URL prefix if present
            const base64Image = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
            const imageBuffer = Buffer.from(base64Image, 'base64');
            
            const { blurIntensity, focusZone, focusIntensity } = options;
            
            const image = sharp(imageBuffer);
            const metadata = await image.metadata();
            const { width, height } = metadata;
            
            // Create a more sophisticated focus effect
            const result = await this.createAdvancedFocusEffect(
                imageBuffer, 
                width, 
                height, 
                blurIntensity, 
                focusZone, 
                focusIntensity
            );
            
            return result;
            
        } catch (error) {
            console.error('Error in applyFocusEffectFromBase64:', error);
            throw new Error(`Failed to process base64 image: ${error.message}`);
        }
    }
    
    /**
     * Creates an advanced focus effect with gradient transitions
     * @param {Buffer} imageBuffer - Original image buffer
     * @param {number} width - Image width
     * @param {number} height - Image height
     * @param {number} blurIntensity - Blur strength
     * @param {Object} focusZone - Focus zone parameters
     * @param {number} focusIntensity - Focus intensity
     * @returns {Buffer} Processed image buffer
     */
    async createAdvancedFocusEffect(imageBuffer, width, height, blurIntensity, focusZone, focusIntensity) {
        try {
            // Create multiple blur levels for smooth transition
            const lightBlur = await sharp(imageBuffer)
                .blur(Math.max(1, blurIntensity * 0.3))
                .toBuffer();
                
            const mediumBlur = await sharp(imageBuffer)
                .blur(Math.max(2, blurIntensity * 0.6))
                .toBuffer();
                
            const heavyBlur = await sharp(imageBuffer)
                .blur(blurIntensity)
                .toBuffer();
            
            // Create gradient masks for smooth transitions
            const focusMask = await this.createGradientFocusMask(width, height, focusZone);
            const mediumMask = await this.createGradientFocusMask(width, height, {
                ...focusZone,
                radius: focusZone.radius * 1.5
            });
            
            // Start with the heavily blurred image as base
            let result = sharp(heavyBlur);
            
            // Composite medium blur with mask
            result = result.composite([
                {
                    input: mediumBlur,
                    blend: 'over',
                    opacity: 0.7
                }
            ]);
            
            // Composite light blur with tighter mask
            result = result.composite([
                {
                    input: lightBlur,
                    blend: 'over',
                    opacity: 0.8
                }
            ]);
            
            // Finally composite the original image in the focus area
            result = result.composite([
                {
                    input: imageBuffer,
                    blend: 'over',
                    opacity: focusIntensity
                }
            ]);
            
            return await result.jpeg({ quality: 90 }).toBuffer();
            
        } catch (error) {
            console.error('Error in createAdvancedFocusEffect:', error);
            throw error;
        }
    }
    
    /**
     * Creates a focus mask with gradient edges
     * @param {number} width - Image width
     * @param {number} height - Image height
     * @param {Object} focusZone - Focus zone parameters
     * @returns {Buffer} Mask buffer
     */
    async createGradientFocusMask(width, height, focusZone) {
        try {
            const { x, y, radius } = focusZone;
            
            // Convert normalized coordinates to pixel coordinates
            const centerX = Math.round(x * width);
            const centerY = Math.round(y * height);
            const radiusPixels = Math.round(radius * Math.min(width, height));
            
            // Create SVG for gradient mask
            const svg = `
                <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <radialGradient id="focusGradient" cx="${x}" cy="${y}" r="${radius}">
                            <stop offset="0%" style="stop-color:white;stop-opacity:1" />
                            <stop offset="70%" style="stop-color:white;stop-opacity:0.8" />
                            <stop offset="100%" style="stop-color:black;stop-opacity:0" />
                        </radialGradient>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#focusGradient)" />
                </svg>
            `;
            
            return await sharp(Buffer.from(svg))
                .png()
                .toBuffer();
                
        } catch (error) {
            console.error('Error in createGradientFocusMask:', error);
            // Fallback to simple circular mask
            return await this.createFocusMask(width, height, focusZone);
        }
    }
    
    /**
     * Creates a simple circular focus mask
     * @param {number} width - Image width
     * @param {number} height - Image height
     * @param {Object} focusZone - Focus zone parameters
     * @returns {Buffer} Mask buffer
     */
    async createFocusMask(width, height, focusZone) {
        try {
            const { x, y, radius } = focusZone;
            
            // Create a simple circular mask
            const svg = `
                <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="${x * width}" cy="${y * height}" r="${radius * Math.min(width, height)}" 
                            fill="white" />
                </svg>
            `;
            
            return await sharp(Buffer.from(svg))
                .png()
                .toBuffer();
                
        } catch (error) {
            console.error('Error in createFocusMask:', error);
            throw error;
        }
    }
}

module.exports = new ProcessingService();
