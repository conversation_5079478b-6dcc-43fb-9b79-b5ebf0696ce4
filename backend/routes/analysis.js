const express = require('express');
const router = express.Router();
const analysisService = require('../services/analysisService');
const fs = require('fs').promises;

/**
 * POST /api/analysis/detect-focus
 * Analyzes an uploaded image to detect focal zones
 */
router.post('/detect-focus', async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ 
                error: 'No image file provided' 
            });
        }

        const imagePath = req.file.path;
        
        // Analyze the image to detect focal zones
        const analysisResult = await analysisService.detectFocalZones(imagePath);
        
        // Clean up uploaded file
        await fs.unlink(imagePath);
        
        res.json({
            success: true,
            data: analysisResult
        });
        
    } catch (error) {
        console.error('Error in focus detection:', error);
        
        // Clean up file if it exists
        if (req.file && req.file.path) {
            try {
                await fs.unlink(req.file.path);
            } catch (unlinkError) {
                console.error('Error cleaning up file:', unlinkError);
            }
        }
        
        res.status(500).json({ 
            error: 'Failed to analyze image',
            message: error.message 
        });
    }
});

/**
 * POST /api/analysis/detect-focus-base64
 * Analyzes a base64 encoded image to detect focal zones
 */
router.post('/detect-focus-base64', async (req, res) => {
    try {
        const { imageData } = req.body;
        
        if (!imageData) {
            return res.status(400).json({ 
                error: 'No image data provided' 
            });
        }
        
        // Analyze the base64 image data
        const analysisResult = await analysisService.detectFocalZonesFromBase64(imageData);
        
        res.json({
            success: true,
            data: analysisResult
        });
        
    } catch (error) {
        console.error('Error in base64 focus detection:', error);
        res.status(500).json({ 
            error: 'Failed to analyze image',
            message: error.message 
        });
    }
});

module.exports = router;
