const express = require('express');
const router = express.Router();
const processingService = require('../services/processingService');
const fs = require('fs').promises;

/**
 * POST /api/processing/apply-focus
 * Applies focus/blur effects to an image based on provided parameters
 */
router.post('/apply-focus', async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ 
                error: 'No image file provided' 
            });
        }

        const imagePath = req.file.path;
        const { 
            blurIntensity = 5, 
            focusZone = { x: 0.5, y: 0.5, radius: 0.3 },
            focusIntensity = 1.0 
        } = req.body;

        // Parse focusZone if it's a string
        let parsedFocusZone = focusZone;
        if (typeof focusZone === 'string') {
            try {
                parsedFocusZone = JSON.parse(focusZone);
            } catch (parseError) {
                console.error('Error parsing focusZone:', parseError);
                parsedFocusZone = { x: 0.5, y: 0.5, radius: 0.3 };
            }
        }
        
        // Apply focus effects to the image
        const processedImageBuffer = await processingService.applyFocusEffect(
            imagePath, 
            {
                blurIntensity: parseFloat(blurIntensity),
                focusZone: parsedFocusZone,
                focusIntensity: parseFloat(focusIntensity)
            }
        );
        
        // Clean up uploaded file
        await fs.unlink(imagePath);
        
        // Return processed image as base64
        const base64Image = processedImageBuffer.toString('base64');
        
        res.json({
            success: true,
            data: {
                processedImage: `data:image/jpeg;base64,${base64Image}`,
                parameters: {
                    blurIntensity: parseFloat(blurIntensity),
                    focusZone: parsedFocusZone,
                    focusIntensity: parseFloat(focusIntensity)
                }
            }
        });
        
    } catch (error) {
        console.error('Error in image processing:', error);
        
        // Clean up file if it exists
        if (req.file && req.file.path) {
            try {
                await fs.unlink(req.file.path);
            } catch (unlinkError) {
                console.error('Error cleaning up file:', unlinkError);
            }
        }
        
        res.status(500).json({ 
            error: 'Failed to process image',
            message: error.message 
        });
    }
});

/**
 * POST /api/processing/apply-focus-base64
 * Applies focus/blur effects to a base64 encoded image
 */
router.post('/apply-focus-base64', async (req, res) => {
    try {
        const { 
            imageData,
            blurIntensity = 5, 
            focusZone = { x: 0.5, y: 0.5, radius: 0.3 },
            focusIntensity = 1.0 
        } = req.body;
        
        if (!imageData) {
            return res.status(400).json({ 
                error: 'No image data provided' 
            });
        }
        
        // Apply focus effects to the base64 image
        const processedImageBuffer = await processingService.applyFocusEffectFromBase64(
            imageData,
            {
                blurIntensity: parseFloat(blurIntensity),
                focusZone,
                focusIntensity: parseFloat(focusIntensity)
            }
        );
        
        // Return processed image as base64
        const base64Image = processedImageBuffer.toString('base64');
        
        res.json({
            success: true,
            data: {
                processedImage: `data:image/jpeg;base64,${base64Image}`,
                parameters: {
                    blurIntensity: parseFloat(blurIntensity),
                    focusZone,
                    focusIntensity: parseFloat(focusIntensity)
                }
            }
        });
        
    } catch (error) {
        console.error('Error in base64 image processing:', error);
        res.status(500).json({ 
            error: 'Failed to process image',
            message: error.message 
        });
    }
});

module.exports = router;
