# Auto Focus Backend

This backend service provides image analysis and processing capabilities for the Auto Focus feature in the Adobe Express addon.

## Features

- **Focal Zone Detection**: Analyzes images to detect areas of focus using edge detection algorithms
- **Focus Effect Processing**: Applies selective blur and focus effects to images
- **Multiple Input Formats**: Supports both file uploads and base64 encoded images
- **Advanced Processing**: Creates gradient transitions for natural-looking focus effects

## API Endpoints

### Analysis Endpoints

#### POST /api/analysis/detect-focus
Analyzes an uploaded image file to detect focal zones.

**Request**: Multipart form data with image file
**Response**:
```json
{
  "success": true,
  "data": {
    "width": 1920,
    "height": 1080,
    "focalZones": [
      {
        "x": 0.5,
        "y": 0.3,
        "radius": 0.2,
        "strength": 150.5
      }
    ],
    "suggestedFocusZone": {
      "x": 0.5,
      "y": 0.3,
      "radius": 0.2
    },
    "analysisComplete": true
  }
}
```

#### POST /api/analysis/detect-focus-base64
Analyzes a base64 encoded image to detect focal zones.

**Request**:
```json
{
  "imageData": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
}
```

### Processing Endpoints

#### POST /api/processing/apply-focus
Applies focus/blur effects to an uploaded image file.

**Request**: Multipart form data with:
- `image`: Image file
- `blurIntensity`: Number (1-20, default: 5)
- `focusZone`: JSON string with {x, y, radius}
- `focusIntensity`: Number (0-1, default: 1.0)

#### POST /api/processing/apply-focus-base64
Applies focus/blur effects to a base64 encoded image.

**Request**:
```json
{
  "imageData": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
  "blurIntensity": 5,
  "focusZone": {
    "x": 0.5,
    "y": 0.5,
    "radius": 0.3
  },
  "focusIntensity": 1.0
}
```

## Installation

1. Navigate to the backend directory:
```bash
cd backend
```

2. Install dependencies:
```bash
npm install
```

3. Start the server:
```bash
npm start
```

For development with auto-reload:
```bash
npm run dev
```

## Configuration

The server runs on port 3001 by default. You can change this by setting the `PORT` environment variable:

```bash
PORT=8080 npm start
```

## Dependencies

- **express**: Web framework
- **sharp**: High-performance image processing
- **multer**: File upload handling
- **cors**: Cross-origin resource sharing
- **opencv4nodejs**: Computer vision capabilities (optional, for advanced analysis)

## Error Handling

All endpoints return consistent error responses:

```json
{
  "error": "Error description",
  "message": "Detailed error message"
}
```

## Security Considerations

- File uploads are limited to 50MB
- Only image file types are accepted
- Uploaded files are automatically cleaned up after processing
- CORS is enabled for frontend integration
