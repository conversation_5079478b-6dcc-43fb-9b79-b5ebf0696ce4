# Quick Start Guide - Auto Focus Adobe Express Add-on (REAL SDK INTEGRATION)

## 🚀 Getting Started in 5 Minutes

### Step 1: Start the Backend Service
```bash
# Navigate to backend directory
cd backend

# Install dependencies (first time only)
npm install

# Start the server
npm start
```

You should see:
```
Auto Focus Backend server is running on port 3001
Health check: http://localhost:3001/health
```

### Step 2: Start the Frontend Add-on
```bash
# Navigate back to project root
cd ..

# Install dependencies (first time only)
npm install

# Start the add-on development server
npm run start
```

The add-on will be built and ready for Adobe Express.

### Step 3: Test the Implementation

#### Option A: Test Backend API Directly
```bash
# Test health endpoint
curl http://localhost:3001/health

# Expected response:
# {"status":"OK","message":"Auto Focus Backend is running"}
```

#### Option B: Use the Add-on Interface (REAL IMPLEMENTATION)
1. Load the add-on in Adobe Express
2. **Select an actual image** in your Adobe Express document
3. The interface will show "Auto Focus" with controls
4. **Real Adobe Express SDK integration**:
   - Click "Analyze" to analyze the actual selected image
   - Adjust the sliders to modify real focus parameters
   - Click "Apply Changes" to replace the image with the processed version

## 🎛️ How to Use the Auto Focus Feature

### 1. Image Selection
- Select an image in your Adobe Express document
- The add-on will detect the selection using **real Adobe Express SDK APIs**

### 2. Analysis
- Click the "Analyze" button
- The backend will analyze the image to detect focal zones
- Suggested focus settings will be applied automatically

### 3. Adjust Settings
Use the sliders to fine-tune the effect:

- **Blur Intensity** (1-20): How strong the blur effect is
- **Horizontal Position** (0-100%): X-coordinate of focus center
- **Vertical Position** (0-100%): Y-coordinate of focus center
- **Focus Area Size** (5-80%): Size of the focused region
- **Focus Intensity** (0-100%): How sharp the focused area appears

### 4. Apply Changes
- Click "Apply Changes" to process the image
- The processed image will **replace the original using real Adobe Express SDK**

### 5. Reset
- Click "Reset" to return to the suggested settings

## 🔧 Troubleshooting

### Backend Issues
**Problem**: "Backend service is not available"
**Solution**:
1. Make sure the backend server is running on port 3001
2. Check if another service is using port 3001
3. Try restarting the backend server

**Problem**: Port 3001 already in use
**Solution**:
```bash
# Kill any process using port 3001
sudo lsof -ti:3001 | xargs kill -9

# Or change the port in backend/server.js
const PORT = process.env.PORT || 3002;
```

### Frontend Issues
**Problem**: Build errors
**Solution**:
1. Delete node_modules and reinstall: `rm -rf node_modules && npm install`
2. Clear webpack cache: `npm run clean`
3. Check for TypeScript errors in the console

**Problem**: Add-on not loading in Adobe Express
**Solution**:
1. Make sure the build completed successfully
2. Check the browser console for errors
3. Verify the manifest.json is valid

## 📝 Development Notes

### ✅ **REAL ADOBE EXPRESS SDK INTEGRATION**
1. **✅ Real Adobe Express SDK**: All image selection and updating functions use actual SDK APIs
2. **✅ Real Image Processing**: Works with actual selected images from Adobe Express documents
3. **✅ Production Ready**: Full Document Sandbox API integration with proper image replacement

### ✅ **Production Features**
1. ✅ Real image selection detection using Document Sandbox API
2. ✅ Actual image rendition and processing with selected images
3. ✅ True image replacement that maintains position and size
4. ✅ Proper error handling for real-world scenarios

## 🎯 What's Working Right Now

✅ **Backend Server**: Fully functional image processing API
✅ **Frontend UI**: Complete interface with all controls
✅ **Image Analysis**: Edge detection and focus zone algorithms
✅ **Image Processing**: Advanced blur and focus effects
✅ **Error Handling**: Proper error states and user feedback
✅ **Responsive Design**: Works on different screen sizes

## 📞 Need Help?

1. Check the console logs in both frontend and backend
2. Verify all dependencies are installed correctly
3. Make sure both servers are running simultaneously
4. Test the backend API endpoints directly with curl

**The implementation is complete with REAL Adobe Express SDK integration and ready for production use! 🎉**
