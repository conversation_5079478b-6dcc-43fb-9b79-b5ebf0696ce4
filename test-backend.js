const axios = require('axios');

// Test the backend API
async function testBackend() {
    const baseURL = 'http://localhost:3001';
    
    try {
        // Test health endpoint
        console.log('Testing health endpoint...');
        const healthResponse = await axios.get(`${baseURL}/health`);
        console.log('Health check:', healthResponse.data);
        
        // Test image analysis endpoint
        console.log('\nTesting image analysis endpoint...');
        const testImageData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
        
        const analysisResponse = await axios.post(`${baseURL}/api/analysis/detect-focus-base64`, {
            imageData: testImageData
        });
        console.log('Analysis result:', JSON.stringify(analysisResponse.data, null, 2));
        
        // Test image processing endpoint
        console.log('\nTesting image processing endpoint...');
        const processingResponse = await axios.post(`${baseURL}/api/processing/apply-focus-base64`, {
            imageData: testImageData,
            blurIntensity: 5,
            focusZone: { x: 0.5, y: 0.5, radius: 0.3 },
            focusIntensity: 1.0
        });
        console.log('Processing result keys:', Object.keys(processingResponse.data));
        console.log('Processing success:', processingResponse.data.success);
        
        console.log('\n✅ All backend tests passed!');
        
    } catch (error) {
        console.error('❌ Backend test failed:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
            console.error('Response status:', error.response.status);
        }
    }
}

testBackend();
