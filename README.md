# Auto Focus Adobe Express Add-on

This Adobe Express add-on provides an advanced Auto Focus feature similar to Canva's Auto Focus tool. It allows users to select an image, analyze it to detect focal zones, and apply selective blur effects to create professional-looking depth-of-field effects.

## Features

- **Intelligent Focus Detection**: Automatically analyzes images to detect areas of focus using edge detection algorithms
- **Interactive Controls**: Intuitive sliders to adjust blur intensity, focus zone position, size, and intensity
- **Real-time Processing**: Backend-powered image processing with immediate visual feedback
- **Professional Results**: Creates smooth gradient transitions for natural-looking focus effects

## Architecture

The project consists of two main components:

### Frontend (Adobe Express Add-on)
- **React + TypeScript**: Modern UI built with React and TypeScript
- **Spectrum Design System**: Uses Adobe's Spectrum Web Components for consistent UI
- **Adobe Express SDK**: Integrates with Adobe Express for image selection and manipulation

### Backend (Node.js Service)
- **Express.js**: RESTful API server for image processing
- **Sharp**: High-performance image processing library
- **Advanced Algorithms**: Edge detection and focus analysis capabilities

## Tools & Technologies

### Frontend
- HTML5, CSS3
- React 18.2.0
- TypeScript 5.3.2
- Adobe Spectrum Web Components
- Axios for API communication
- Webpack for bundling

### Backend
- Node.js
- Express.js 4.18.2
- Sharp 0.32.6 (Image processing)
- Multer 2.0.0 (File uploads)
- CORS enabled for cross-origin requests

## Setup Instructions

### Prerequisites
- Node.js 18.0.0 or higher
- npm or yarn package manager

### Frontend Setup
1. Install the frontend dependencies:
   ```bash
   npm install
   ```

2. Build the application:
   ```bash
   npm run build
   ```

3. Start the development server:
   ```bash
   npm run start
   ```

### Backend Setup
1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install backend dependencies:
   ```bash
   npm install
   ```

3. Start the backend server:
   ```bash
   npm start
   ```

   For development with auto-reload:
   ```bash
   npm run dev
   ```

The backend server will run on `http://localhost:3001` by default.

## Usage

1. **Start the Backend**: Make sure the backend server is running on port 3001
2. **Load the Add-on**: Install and load the add-on in Adobe Express
3. **Select an Image**: Choose an image in your Adobe Express document
4. **Analyze**: Click the "Analyze" button to detect focal zones
5. **Adjust Settings**: Use the sliders to fine-tune:
   - **Blur Intensity**: Controls how strong the blur effect is (1-20)
   - **Horizontal/Vertical Position**: Adjusts the center of the focus area (0-100%)
   - **Focus Area Size**: Controls the size of the focused region (5-80%)
   - **Focus Intensity**: Controls how sharp the focused area appears (0-100%)
6. **Apply**: Click "Apply Changes" to process and update the image
7. **Reset**: Use "Reset" to return to the suggested settings

## API Endpoints

### Analysis
- `POST /api/analysis/detect-focus-base64`: Analyzes base64 image data to detect focal zones

### Processing
- `POST /api/processing/apply-focus-base64`: Applies focus/blur effects to base64 image data

### Health Check
- `GET /health`: Returns server status

## Project Structure

```
├── src/                          # Frontend source code
│   ├── components/              # React components
│   │   ├── App.tsx             # Main application component
│   │   ├── FocusControls.tsx   # Focus adjustment controls
│   │   ├── LoadingSpinner.tsx  # Loading indicator
│   │   └── App.css             # Component styles
│   ├── utils/                  # Utility functions
│   │   ├── apiClient.ts        # Backend API communication
│   │   └── imageUtils.ts       # Adobe Express SDK helpers
│   ├── index.tsx               # Application entry point
│   ├── index.html              # HTML template
│   └── manifest.json           # Add-on manifest
├── backend/                     # Backend source code
│   ├── routes/                 # API route handlers
│   │   ├── analysis.js         # Image analysis endpoints
│   │   └── processing.js       # Image processing endpoints
│   ├── services/               # Business logic services
│   │   ├── analysisService.js  # Focus detection algorithms
│   │   └── processingService.js # Image processing logic
│   ├── server.js               # Express server setup
│   ├── package.json            # Backend dependencies
│   └── README.md               # Backend documentation
├── package.json                # Frontend dependencies
├── webpack.config.js           # Webpack configuration
├── tsconfig.json              # TypeScript configuration
└── README.md                  # This file
```

## Development

### Adding New Features
1. **Frontend**: Add new components in `src/components/`
2. **Backend**: Add new routes in `backend/routes/` and services in `backend/services/`
3. **API**: Update `src/utils/apiClient.ts` for new endpoints

### Testing
- Frontend: `npm run build` to check for compilation errors
- Backend: `npm start` to verify server functionality
- Integration: Test the full workflow from image selection to processing

## Troubleshooting

### Common Issues

1. **Backend not available**:
   - Ensure the backend server is running on port 3001
   - Check firewall settings
   - Verify all backend dependencies are installed

2. **Image selection not working**:
   - Make sure an image is selected in Adobe Express
   - Check browser console for SDK-related errors

3. **Processing fails**:
   - Verify image format is supported (JPEG, PNG, GIF, BMP, WebP)
   - Check backend logs for processing errors
   - Ensure sufficient memory for image processing

### Debug Mode
Enable debug logging by setting environment variables:
```bash
# Backend
DEBUG=auto-focus:* npm start

# Frontend
NODE_ENV=development npm start
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
