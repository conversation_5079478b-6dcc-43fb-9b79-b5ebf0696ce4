import React from 'react';
import { ProgressCircle } from '@swc-react/progress-circle';

interface LoadingSpinnerProps {
    message?: string;
    size?: 's' | 'm' | 'l';
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
    message = 'Processing...', 
    size = 'm' 
}) => {
    return (
        <div className="loading-spinner">
            <ProgressCircle size={size} indeterminate />
            <p className="loading-message">{message}</p>
        </div>
    );
};

export default LoadingSpinner;
