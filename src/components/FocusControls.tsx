import React from 'react';
import { But<PERSON> } from '@swc-react/button';
import { FocalZone } from '../utils/apiClient';

interface FocusControlsProps {
    blurIntensity: number;
    focusIntensity: number;
    focusZone: FocalZone;
    onBlurIntensityChange: (value: number) => void;
    onFocusIntensityChange: (value: number) => void;
    onFocusZoneChange: (zone: FocalZone) => void;
    onApplyChanges: () => void;
    onReset: () => void;
    isProcessing: boolean;
    disabled?: boolean;
}

const FocusControls: React.FC<FocusControlsProps> = ({
    blurIntensity,
    focusIntensity,
    focusZone,
    onBlurIntensityChange,
    onFocusIntensityChange,
    onFocusZoneChange,
    onApplyChanges,
    onReset,
    isProcessing,
    disabled = false
}) => {
    const handleFocusZoneXChange = (value: number) => {
        onFocusZoneChange({
            ...focusZone,
            x: value / 100 // Convert from 0-100 to 0-1
        });
    };

    const handleFocusZoneYChange = (value: number) => {
        onFocusZoneChange({
            ...focusZone,
            y: value / 100 // Convert from 0-100 to 0-1
        });
    };

    const handleFocusZoneRadiusChange = (value: number) => {
        onFocusZoneChange({
            ...focusZone,
            radius: value / 100 // Convert from 0-100 to 0-1
        });
    };

    return (
        <div className="focus-controls">
            <div className="control-section">
                <h3>Blur Settings</h3>

                <div className="control-item">
                    <label htmlFor="blur-intensity">Blur Intensity</label>
                    <div className="slider-container">
                        <input
                            type="range"
                            id="blur-intensity"
                            min={1}
                            max={20}
                            step={1}
                            value={blurIntensity}
                            onChange={(event) => onBlurIntensityChange(Number(event.target.value))}
                            disabled={disabled || isProcessing}
                            className="custom-slider"
                        />
                        <span className="control-value">{blurIntensity}</span>
                    </div>
                </div>
            </div>

            <div className="control-section">
                <h3>Focus Zone</h3>

                <div className="control-item">
                    <label htmlFor="focus-x">Horizontal Position</label>
                    <div className="slider-container">
                        <input
                            type="range"
                            id="focus-x"
                            min={0}
                            max={100}
                            step={1}
                            value={Math.round(focusZone.x * 100)}
                            onChange={(event) => handleFocusZoneXChange(Number(event.target.value))}
                            disabled={disabled || isProcessing}
                            className="custom-slider"
                        />
                        <span className="control-value">{Math.round(focusZone.x * 100)}%</span>
                    </div>
                </div>

                <div className="control-item">
                    <label htmlFor="focus-y">Vertical Position</label>
                    <div className="slider-container">
                        <input
                            type="range"
                            id="focus-y"
                            min={0}
                            max={100}
                            step={1}
                            value={Math.round(focusZone.y * 100)}
                            onChange={(event) => handleFocusZoneYChange(Number(event.target.value))}
                            disabled={disabled || isProcessing}
                            className="custom-slider"
                        />
                        <span className="control-value">{Math.round(focusZone.y * 100)}%</span>
                    </div>
                </div>

                <div className="control-item">
                    <label htmlFor="focus-radius">Focus Area Size</label>
                    <div className="slider-container">
                        <input
                            type="range"
                            id="focus-radius"
                            min={5}
                            max={80}
                            step={1}
                            value={Math.round(focusZone.radius * 100)}
                            onChange={(event) => handleFocusZoneRadiusChange(Number(event.target.value))}
                            disabled={disabled || isProcessing}
                            className="custom-slider"
                        />
                        <span className="control-value">{Math.round(focusZone.radius * 100)}%</span>
                    </div>
                </div>

                <div className="control-item">
                    <label htmlFor="focus-intensity">Focus Intensity</label>
                    <div className="slider-container">
                        <input
                            type="range"
                            id="focus-intensity"
                            min={0}
                            max={100}
                            step={5}
                            value={Math.round(focusIntensity * 100)}
                            onChange={(event) => onFocusIntensityChange(Number(event.target.value) / 100)}
                            disabled={disabled || isProcessing}
                            className="custom-slider"
                        />
                        <span className="control-value">{Math.round(focusIntensity * 100)}%</span>
                    </div>
                </div>
            </div>

            <div className="control-actions">
                <Button
                    size="m"
                    variant="primary"
                    onClick={onApplyChanges}
                    disabled={disabled || isProcessing}
                >
                    {isProcessing ? 'Processing...' : 'Apply Changes'}
                </Button>

                <Button
                    size="m"
                    variant="secondary"
                    onClick={onReset}
                    disabled={disabled || isProcessing}
                >
                    Reset
                </Button>
            </div>
        </div>
    );
};

export default FocusControls;
