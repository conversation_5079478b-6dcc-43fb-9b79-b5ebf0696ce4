// To support: system="express" scale="medium" color="light"
// import these spectrum web components modules:
import "@spectrum-web-components/theme/express/scale-medium.js";
import "@spectrum-web-components/theme/express/theme-light.js";

// To learn more about using "swc-react" visit:
// https://opensource.adobe.com/spectrum-web-components/using-swc-react/
import { Button } from "@swc-react/button";
import { Theme } from "@swc-react/theme";
import React, { useState, useEffect } from "react";
import "./App.css";

import { AddOnSDKAPI } from "https://new.express.adobe.com/static/add-on-sdk/sdk.js";

// Import our custom components and utilities
import LoadingSpinner from "./LoadingSpinner";
import FocusControls from "./FocusControls";
import { analyzeImage, applyFocusEffect, checkBackendHealth, FocalZone, AnalysisResult } from "../utils/apiClient";
import { getSelectedImage, updateSelectedImage, hasSelectedImage } from "../utils/imageUtils";

interface AppState {
    isAnalyzing: boolean;
    isProcessing: boolean;
    hasImage: boolean;
    analysisResult: AnalysisResult | null;
    currentImageData: string | null;
    blurIntensity: number;
    focusIntensity: number;
    focusZone: FocalZone;
    backendAvailable: boolean;
    error: string | null;
}

const App = ({ addOnUISdk }: { addOnUISdk: AddOnSDKAPI }) => {
    const [state, setState] = useState<AppState>({
        isAnalyzing: false,
        isProcessing: false,
        hasImage: false,
        analysisResult: null,
        currentImageData: null,
        blurIntensity: 5,
        focusIntensity: 1.0,
        focusZone: { x: 0.5, y: 0.5, radius: 0.3 },
        backendAvailable: false,
        error: null
    });

    // Check backend availability on component mount
    useEffect(() => {
        checkBackendHealth().then(available => {
            setState(prev => ({
                ...prev,
                backendAvailable: available,
                error: available ? null : 'Backend service is not available. Please start the backend server.'
            }));
        });
    }, []);

    // Check for selected image on mount
    useEffect(() => {
        const checkSelection = async () => {
            try {
                const hasImg = await hasSelectedImage(addOnUISdk);
                setState(prev => ({ ...prev, hasImage: hasImg }));
            } catch (error) {
                console.error('Error checking selection:', error);
            }
        };

        checkSelection();
    }, [addOnUISdk]);

    const handleAnalyzeImage = async () => {
        if (!state.backendAvailable) {
            setState(prev => ({ ...prev, error: 'Backend service is not available' }));
            return;
        }

        setState(prev => ({ ...prev, isAnalyzing: true, error: null }));

        try {
            // Get the selected image
            const imageData = await getSelectedImage(addOnUISdk);

            if (!imageData) {
                throw new Error('No image data received');
            }

            // Analyze the image
            const analysisResult = await analyzeImage(imageData);

            setState(prev => ({
                ...prev,
                isAnalyzing: false,
                analysisResult,
                currentImageData: imageData,
                focusZone: analysisResult.suggestedFocusZone
            }));

        } catch (error) {
            console.error('Error analyzing image:', error);
            setState(prev => ({
                ...prev,
                isAnalyzing: false,
                error: error instanceof Error ? error.message : 'Failed to analyze image'
            }));
        }
    };

    const handleApplyFocus = async () => {
        if (!state.currentImageData) {
            setState(prev => ({ ...prev, error: 'No image data available' }));
            return;
        }

        setState(prev => ({ ...prev, isProcessing: true, error: null }));

        try {
            // Apply focus effect
            const result = await applyFocusEffect(
                state.currentImageData,
                state.blurIntensity,
                state.focusZone,
                state.focusIntensity
            );

            // Update the image in Adobe Express
            await updateSelectedImage(addOnUISdk, result.processedImage);

            setState(prev => ({ ...prev, isProcessing: false }));

        } catch (error) {
            console.error('Error applying focus effect:', error);
            setState(prev => ({
                ...prev,
                isProcessing: false,
                error: error instanceof Error ? error.message : 'Failed to apply focus effect'
            }));
        }
    };

    const handleReset = () => {
        setState(prev => ({
            ...prev,
            blurIntensity: 5,
            focusIntensity: 1.0,
            focusZone: state.analysisResult?.suggestedFocusZone || { x: 0.5, y: 0.5, radius: 0.3 },
            error: null
        }));
    };

    const handleRefreshSelection = async () => {
        try {
            const hasImg = await hasSelectedImage(addOnUISdk);
            setState(prev => ({ ...prev, hasImage: hasImg }));
        } catch (error) {
            console.error('Error refreshing selection:', error);
        }
    };

    return (
        <Theme system="express" scale="medium" color="light">
            <div className="container">
                <div className="auto-focus-app">
                    <h2>Auto Focus</h2>

                    {state.error && (
                        <div className="error-message">
                            <p>{state.error}</p>
                        </div>
                    )}

                    {!state.backendAvailable && (
                        <div className="warning-message">
                            <p>Backend service is not available. Please start the backend server.</p>
                        </div>
                    )}

                    {!state.hasImage ? (
                        <div className="no-selection">
                            <p>Please select an image to use Auto Focus.</p>
                            <Button size="m" onClick={handleRefreshSelection}>
                                Refresh Selection
                            </Button>
                        </div>
                    ) : (
                        <div className="image-selected">
                            {!state.analysisResult ? (
                                <div className="analyze-section">
                                    <p>Image selected. Click Analyze to detect focus areas.</p>
                                    {state.isAnalyzing ? (
                                        <LoadingSpinner message="Analyzing image..." />
                                    ) : (
                                        <Button
                                            size="m"
                                            variant="primary"
                                            onClick={handleAnalyzeImage}
                                            disabled={!state.backendAvailable}
                                        >
                                            Analyze
                                        </Button>
                                    )}
                                </div>
                            ) : (
                                <div className="controls-section">
                                    <p>Analysis complete! Adjust the focus settings below:</p>

                                    <FocusControls
                                        blurIntensity={state.blurIntensity}
                                        focusIntensity={state.focusIntensity}
                                        focusZone={state.focusZone}
                                        onBlurIntensityChange={(value) =>
                                            setState(prev => ({ ...prev, blurIntensity: value }))
                                        }
                                        onFocusIntensityChange={(value) =>
                                            setState(prev => ({ ...prev, focusIntensity: value }))
                                        }
                                        onFocusZoneChange={(zone) =>
                                            setState(prev => ({ ...prev, focusZone: zone }))
                                        }
                                        onApplyChanges={handleApplyFocus}
                                        onReset={handleReset}
                                        isProcessing={state.isProcessing}
                                        disabled={!state.backendAvailable}
                                    />

                                    {state.isProcessing && (
                                        <LoadingSpinner message="Processing image..." />
                                    )}
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </Theme>
    );
};

export default App;
