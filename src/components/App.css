.container {
    margin: 24px;
    display: flex;
    flex-direction: column;
}

/* Auto Focus App Styles */
.auto-focus-app {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.auto-focus-app h2 {
    margin: 0 0 16px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--spectrum-global-color-gray-900);
}

/* Message Styles */
.error-message {
    padding: 12px;
    background-color: var(--spectrum-global-color-red-100);
    border: 1px solid var(--spectrum-global-color-red-400);
    border-radius: 4px;
    color: var(--spectrum-global-color-red-700);
}

.warning-message {
    padding: 12px;
    background-color: var(--spectrum-global-color-orange-100);
    border: 1px solid var(--spectrum-global-color-orange-400);
    border-radius: 4px;
    color: var(--spectrum-global-color-orange-700);
}

.error-message p,
.warning-message p {
    margin: 0;
    font-size: 14px;
}

/* Section Styles */
.no-selection,
.analyze-section,
.controls-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 16px;
    border: 1px solid var(--spectrum-global-color-gray-300);
    border-radius: 8px;
    background-color: var(--spectrum-global-color-gray-50);
}

.no-selection p,
.analyze-section p,
.controls-section p {
    margin: 0;
    font-size: 14px;
    color: var(--spectrum-global-color-gray-700);
}

/* Loading Spinner Styles */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 20px;
}

.loading-message {
    font-size: 14px;
    color: var(--spectrum-global-color-gray-600);
    margin: 0;
}

/* Focus Controls Styles */
.focus-controls {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.control-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.control-section h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--spectrum-global-color-gray-800);
    border-bottom: 1px solid var(--spectrum-global-color-gray-300);
    padding-bottom: 8px;
}

.control-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-item label {
    font-size: 14px;
    font-weight: 500;
    color: var(--spectrum-global-color-gray-700);
}

.slider-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.custom-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: var(--spectrum-global-color-gray-300);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.custom-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--spectrum-global-color-blue-500);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--spectrum-global-color-blue-500);
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom-slider:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.custom-slider:disabled::-webkit-slider-thumb {
    cursor: not-allowed;
    background: var(--spectrum-global-color-gray-400);
}

.custom-slider:disabled::-moz-range-thumb {
    cursor: not-allowed;
    background: var(--spectrum-global-color-gray-400);
}

.control-value {
    font-size: 12px;
    color: var(--spectrum-global-color-gray-600);
    text-align: right;
    min-width: 40px;
    font-weight: 500;
}

.control-actions {
    display: flex;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid var(--spectrum-global-color-gray-300);
}

.control-actions sp-button {
    flex: 1;
}

/* Responsive Design */
@media (max-width: 480px) {
    .container {
        margin: 16px;
    }

    .control-actions {
        flex-direction: column;
    }

    .control-actions sp-button {
        width: 100%;
    }
}
