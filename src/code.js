import addOnSandboxSdk from "https://new.express.adobe.com/static/add-on-sdk/sdk.js";

// Wait for the SDK to be ready
addOnSandboxSdk.ready.then(async () => {
    console.log("Document Sandbox SDK is ready");

    // Make the SDK available globally for the UI to access
    window.addOnSandboxSdk = addOnSandboxSdk;

    // Get the editor instance
    const { editor } = addOnSandboxSdk;

    // Listen for selection changes
    editor.on("selectionchange", () => {
        console.log("Selection changed");
        // Notify the UI about selection changes if needed
        // This could be used to update the UI state
    });

    // Helper function to get selected image nodes
    function getSelectedImageNodes() {
        const selection = editor.context.selection;
        return selection.filter(node =>
            node.type === 'MediaContainerNode' ||
            node.type === 'ImageRectangleNode' ||
            (node.mediaRectangle && node.mediaRectangle.image)
        );
    }

    // Helper function to create renditions of selected images
    async function createImageRenditions(imageNodes, options = {}) {
        const renditions = [];

        for (const node of imageNodes) {
            try {
                // Create a rendition of the specific node
                const renditionOptions = {
                    node: node,
                    format: options.format || 'png',
                    backgroundColor: options.backgroundColor || 0xFFFFFF,
                    ...options
                };

                // Note: This is a simplified approach
                // The actual implementation might need to use different methods
                // depending on the specific Adobe Express SDK version
                const rendition = await editor.createRendition(renditionOptions);
                renditions.push(rendition);
            } catch (error) {
                console.error('Error creating rendition for node:', error);
            }
        }

        return renditions;
    }

    // Helper function to replace an image node with a new image
    async function replaceImageNode(originalNode, newImageBlob) {
        try {
            // Load the new image as a bitmap
            const bitmapImage = await editor.loadBitmapImage(newImageBlob);

            // Create a new image container with the processed image
            const newImageContainer = editor.createImageContainer(bitmapImage, {
                initialSize: {
                    width: originalNode.boundsInParent.width,
                    height: originalNode.boundsInParent.height
                }
            });

            // Position the new image at the same location as the original
            newImageContainer.setPositionInParent(
                originalNode.boundsInParent.x,
                originalNode.boundsInParent.y
            );

            // Get the parent container
            const parent = originalNode.parent;

            // Add the new image to the parent
            parent.children.append(newImageContainer);

            // Remove the original image
            originalNode.removeFromParent();

            console.log('Image replaced successfully');
            return newImageContainer;

        } catch (error) {
            console.error('Error replacing image node:', error);
            throw error;
        }
    }

    // Expose helper functions globally for the UI to use
    window.documentSandboxHelpers = {
        getSelectedImageNodes,
        createImageRenditions,
        replaceImageNode,
        editor
    };

    console.log("Document Sandbox helpers are ready");
});

// Handle any errors
addOnSandboxSdk.ready.catch((error) => {
    console.error("Failed to initialize Document Sandbox SDK:", error);
});
