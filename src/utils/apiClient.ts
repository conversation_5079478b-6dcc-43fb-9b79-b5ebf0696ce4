import axios from 'axios';

// Backend API base URL - adjust this based on your backend deployment
const API_BASE_URL = 'http://localhost:3001/api';

// Create axios instance with default configuration
const apiClient = axios.create({
    baseURL: API_BASE_URL,
    timeout: 30000, // 30 seconds timeout
    headers: {
        'Content-Type': 'application/json',
    },
});

// Types for API responses
export interface FocalZone {
    x: number;
    y: number;
    radius: number;
    strength?: number;
}

export interface AnalysisResult {
    width: number;
    height: number;
    focalZones: FocalZone[];
    suggestedFocusZone: FocalZone;
    analysisComplete: boolean;
}

export interface ProcessingResult {
    processedImage: string; // base64 data URL
    parameters: {
        blurIntensity: number;
        focusZone: FocalZone;
        focusIntensity: number;
    };
}

export interface ApiResponse<T> {
    success: boolean;
    data: T;
    error?: string;
    message?: string;
}

/**
 * Analyzes an image to detect focal zones
 * @param imageData Base64 encoded image data
 * @returns Promise with analysis result
 */
export async function analyzeImage(imageData: string): Promise<AnalysisResult> {
    try {
        const response = await apiClient.post<ApiResponse<AnalysisResult>>(
            '/analysis/detect-focus-base64',
            { imageData }
        );
        
        if (response.data.success) {
            return response.data.data;
        } else {
            throw new Error(response.data.error || 'Analysis failed');
        }
    } catch (error) {
        console.error('Error analyzing image:', error);
        if (axios.isAxiosError(error)) {
            throw new Error(error.response?.data?.message || error.message);
        }
        throw error;
    }
}

/**
 * Applies focus effects to an image
 * @param imageData Base64 encoded image data
 * @param blurIntensity Blur intensity (1-20)
 * @param focusZone Focus zone parameters
 * @param focusIntensity Focus intensity (0-1)
 * @returns Promise with processed image result
 */
export async function applyFocusEffect(
    imageData: string,
    blurIntensity: number,
    focusZone: FocalZone,
    focusIntensity: number
): Promise<ProcessingResult> {
    try {
        const response = await apiClient.post<ApiResponse<ProcessingResult>>(
            '/processing/apply-focus-base64',
            {
                imageData,
                blurIntensity,
                focusZone,
                focusIntensity
            }
        );
        
        if (response.data.success) {
            return response.data.data;
        } else {
            throw new Error(response.data.error || 'Processing failed');
        }
    } catch (error) {
        console.error('Error processing image:', error);
        if (axios.isAxiosError(error)) {
            throw new Error(error.response?.data?.message || error.message);
        }
        throw error;
    }
}

/**
 * Checks if the backend service is available
 * @returns Promise<boolean>
 */
export async function checkBackendHealth(): Promise<boolean> {
    try {
        const response = await axios.get(`${API_BASE_URL.replace('/api', '')}/health`, {
            timeout: 5000
        });
        return response.status === 200;
    } catch (error) {
        console.error('Backend health check failed:', error);
        return false;
    }
}

export default apiClient;
