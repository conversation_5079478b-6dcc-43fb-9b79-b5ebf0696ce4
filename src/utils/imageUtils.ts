import { AddOnSDKAPI } from "https://new.express.adobe.com/static/add-on-sdk/sdk.js";

// Types for Document Sandbox API
interface DocumentSandboxAPI {
    editor: {
        context: {
            selection: any[];
            insertionParent: any;
        };
        documentRoot: any;
        createRenditions: (options: any) => Promise<any[]>;
    };
}

// Types for Document Sandbox helpers
interface DocumentSandboxHelpers {
    getSelectedImageNodes: () => any[];
    createImageRenditions: (nodes: any[], options?: any) => Promise<any[]>;
    replaceImageNode: (originalNode: any, newImageBlob: Blob) => Promise<any>;
    editor: any;
}

// Global reference to Document Sandbox API (if available)
declare global {
    interface Window {
        addOnSandboxSdk?: DocumentSandboxAPI;
        documentSandboxHelpers?: DocumentSandboxHelpers;
    }
}

/**
 * Gets the currently selected image from Adobe Express using Document Sandbox API
 * @param addOnUISdk Adobe Express SDK instance
 * @returns Promise with image data as base64 string
 */
export async function getSelectedImage(addOnUISdk: AddOnSDKAPI): Promise<string | null> {
    try {
        console.log('Getting selected image from Adobe Express...');

        // Try to use Document Sandbox helpers first (preferred approach)
        if (window.documentSandboxHelpers) {
            const helpers = window.documentSandboxHelpers;
            const imageNodes = helpers.getSelectedImageNodes();

            if (!imageNodes || imageNodes.length === 0) {
                throw new Error('No image found in selection. Please select an image.');
            }

            // Get the first selected image
            const imageNode = imageNodes[0];

            // Create a rendition of the selected image
            const renditions = await helpers.createImageRenditions([imageNode], {
                format: 'png',
                backgroundColor: 0xFFFFFF
            });

            if (renditions && renditions.length > 0) {
                const blob = renditions[0].blob;
                const base64 = await blobToBase64(blob);
                return base64;
            }
        }

        // Fallback: Try Document Sandbox API directly
        if (window.addOnSandboxSdk?.editor) {
            const editor = window.addOnSandboxSdk.editor;
            const selection = editor.context.selection;

            if (!selection || selection.length === 0) {
                throw new Error('No items selected. Please select an image first.');
            }

            // Find the first image node in selection
            const imageNode = selection.find((node: any) =>
                node.type === 'MediaContainerNode' ||
                node.type === 'ImageRectangleNode' ||
                (node.mediaRectangle && node.mediaRectangle.image)
            );

            if (!imageNode) {
                throw new Error('No image found in selection. Please select an image.');
            }

            console.log('Found image node, creating rendition...');
            // For now, fall back to page rendition since node-specific rendition might not be available
        }

        // Final fallback: Use current page rendition approach
        console.log('Using current page rendition as fallback...');
        const renditionOptions = {
            range: addOnUISdk.constants.Range.currentPage,
            format: addOnUISdk.constants.RenditionFormat.png,
            backgroundColor: 0xFFFFFF
        };

        const renditions = await addOnUISdk.app.document.createRenditions(
            renditionOptions,
            addOnUISdk.constants.RenditionIntent.export
        );

        if (renditions && renditions.length > 0) {
            const blob = renditions[0].blob;
            const base64 = await blobToBase64(blob);
            return base64;
        }

        throw new Error('No image data could be retrieved');

    } catch (error) {
        console.error('Error getting selected image:', error);
        throw error;
    }
}

/**
 * Updates the selected image in Adobe Express with processed image data
 * @param addOnUISdk Adobe Express SDK instance
 * @param processedImageData Base64 encoded processed image
 * @returns Promise<void>
 */
export async function updateSelectedImage(
    addOnUISdk: AddOnSDKAPI,
    processedImageData: string
): Promise<void> {
    try {
        console.log('Updating image in Adobe Express...');

        // Convert base64 to blob
        const blob = base64ToBlob(processedImageData);

        // Try to use Document Sandbox helpers to replace the selected image
        if (window.documentSandboxHelpers) {
            const helpers = window.documentSandboxHelpers;
            const imageNodes = helpers.getSelectedImageNodes();

            if (imageNodes && imageNodes.length > 0) {
                // Replace the first selected image with the processed version
                const originalNode = imageNodes[0];
                await helpers.replaceImageNode(originalNode, blob);
                console.log('Selected image replaced successfully');
                return;
            }
        }

        // Fallback: Add the processed image as a new image to the document
        console.log('No selected image to replace, adding as new image...');
        await addOnUISdk.app.document.addImage(blob, {
            title: 'Auto Focus Processed Image'
        });

        console.log('Processed image added to document successfully');

    } catch (error) {
        console.error('Error updating image:', error);
        throw new Error(`Failed to update image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

/**
 * Converts ArrayBuffer to base64 string
 * @param buffer ArrayBuffer to convert
 * @returns Base64 string
 */
export function arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
}

/**
 * Converts base64 data URL to Blob
 * @param dataUrl Base64 data URL
 * @returns Blob object
 */
export function base64ToBlob(dataUrl: string): Blob {
    const arr = dataUrl.split(',');
    const mimeMatch = arr[0].match(/:(.*?);/);
    const mime = mimeMatch ? mimeMatch[1] : 'image/jpeg';
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);

    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }

    return new Blob([u8arr], { type: mime });
}

/**
 * Converts a Blob to base64 data URL
 * @param blob Blob to convert
 * @returns Promise with base64 data URL
 */
export async function blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
            if (typeof reader.result === 'string') {
                resolve(reader.result);
            } else {
                reject(new Error('Failed to convert blob to base64'));
            }
        };
        reader.onerror = () => reject(new Error('Failed to read blob'));
        reader.readAsDataURL(blob);
    });
}

/**
 * Validates if the current selection contains an image
 * @param addOnUISdk Adobe Express SDK instance
 * @returns Promise<boolean>
 */
export async function hasSelectedImage(addOnUISdk: AddOnSDKAPI): Promise<boolean> {
    try {
        console.log('Checking for selected image...');

        // Try to use Document Sandbox helpers first
        if (window.documentSandboxHelpers) {
            const helpers = window.documentSandboxHelpers;
            const imageNodes = helpers.getSelectedImageNodes();
            return imageNodes && imageNodes.length > 0;
        }

        // Fallback: Try Document Sandbox API directly
        if (window.addOnSandboxSdk?.editor) {
            const editor = window.addOnSandboxSdk.editor;
            const selection = editor.context.selection;

            if (!selection || selection.length === 0) {
                return false;
            }

            // Check if any selected item is an image
            const hasImage = selection.some((node: any) =>
                node.type === 'MediaContainerNode' ||
                node.type === 'ImageRectangleNode' ||
                (node.mediaRectangle && node.mediaRectangle.image)
            );

            return hasImage;
        }

        // Final fallback: assume there might be content on the current page
        // In a real implementation, you might want to check page metadata
        console.log('Document Sandbox API not available, assuming content exists');
        return true;

    } catch (error) {
        console.error('Error checking for selected image:', error);
        return false;
    }
}

/**
 * Gets image dimensions from base64 data
 * @param base64Data Base64 encoded image data
 * @returns Promise with width and height
 */
export function getImageDimensions(base64Data: string): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
        const img = new Image();

        img.onload = () => {
            resolve({
                width: img.naturalWidth,
                height: img.naturalHeight
            });
        };

        img.onerror = () => {
            reject(new Error('Failed to load image for dimension calculation'));
        };

        img.src = base64Data;
    });
}
