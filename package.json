{"name": "test-addon", "version": "1.0.0", "description": "Adobe Creative Cloud Web Add-on.", "scripts": {"clean": "ccweb-add-on-scripts clean", "build": "ccweb-add-on-scripts build --use webpack", "start": "ccweb-add-on-scripts start --use webpack", "package": "ccweb-add-on-scripts package --use webpack"}, "keywords": ["Adobe", "Creative Cloud Web", "Add-on", "panel"], "dependencies": {"@swc-react/button": "1.0.3", "@swc-react/theme": "1.0.3", "@swc-react/slider": "1.0.3", "@swc-react/progress-circle": "1.0.3", "axios": "^1.6.0", "react": "18.2.0", "react-dom": "18.2.0"}, "devDependencies": {"@adobe/ccweb-add-on-scripts": "^3.0.0", "@types/adobe__ccweb-add-on-sdk": "^1.3.0", "@types/react": "18.2.21", "@types/react-dom": "18.2.7", "copy-webpack-plugin": "11.0.0", "css-loader": "6.8.1", "html-webpack-plugin": "5.5.3", "style-loader": "3.3.3", "ts-loader": "9.5.1", "typescript": "5.3.2", "webpack": "5.98.0", "webpack-cli": "6.0.1"}}